
import os

input_dir_name = '/data3/data_xzl/test'
entity_output_dir_name = '/data3/kg_data/20250825/entity'
relation_output_dir_name = '/data3/kg_data/20250825/relation'


def get_relation(file):
    with open(input_dir_name + '/' + file, 'r') as f:
        lines = f.readlines()
    document = lines[0].strip()
    document_info = eval(document)
    document_content = document_info['content']
    document_id = document_info['id']

    fo = open(relation_output_dir_name + '/relation_' + file, 'w')

    for i in range(1, len(lines)):
        data = {}
        line = lines[i].strip()
        line_info = eval(line)
        line_id = line_info['id']
        if line_info['label'] == 'text':
            data['label'] = 'document_text_relation'
            data['formerId'] = document_id
            data['laterId'] = line_info['id']
            data['properties'] = {'document_id': document_id, 'text_id': line_id}

        elif line_info['label'] == 'table':
            data['label'] = 'document_table_relation'
            data['formerId'] = document_id
            data['laterId'] = line_info['id']
            data['properties'] = {'document_id': document_id, 'table_id': line_id}

        fo.write(str(data) + '\n')

def get_entity(file):
    fo = open(entity_output_dir_name+'/entity_'+file,'w')

    with open(input_dir_name + '/' + file, 'r') as f:
        j = 0
        for i in f:
            j=j+1
            print(j)
            print(i)
            i = i.strip()
            line_info = eval(i)
            label = line_info['label']
            content = line_info['content']
            id = line_info['id']
            data = {"label":label,"id":id,"properties":{"content":content,"serial_number":str(j)}}
            fo.write(str(data)+'\n')

    fo.close()


if __name__ == '__main__':
    file_list = os.listdir(input_dir_name)
    for file in file_list:
        get_relation(file)
        get_entity(file)
