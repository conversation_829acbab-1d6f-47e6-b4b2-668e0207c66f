import time

from milvus_helpers import MilvusHelper
from pymilvus import utility,Collection
import requests
milvushelper = MilvusHelper()
collection_name = "productQA_0810_mid"
milvushelper.create_collection("productQA_0810_mid")
collection = Collection("productQA_0810_mid")
print(collection)

import os

except_file_path = "D:\JAVA\workspace\AI\ZTE_AI\zte-ibo-acm-productretrieve_0804\exceptmid08151.txt"

def truncate_string(s, max_bytes):
    # 将字符串编码为字节
    encoded_string = s.encode('utf-8')
    # 如果字符串长度小于等于目标长度，则无需截断，直接返回
    if len(encoded_string) <= max_bytes:
        return s
    # 否则，截取字符串，使其长度小于目标长度
    truncated_bytes = encoded_string[:max_bytes]
    # 将字节解码为字符串
    truncated_string = truncated_bytes.decode('utf-8', 'ignore')  # 忽略无法解码的字节
    return truncated_string

def embedding_doc(query):
    receive = requests.post( 
        json = {"text":query},
        url = "https://llm.dev.zte.com.cn/zte-igpt-ellm-vector/vector",
        verify = False
    )
    try:
        result = receive.json()['bo']['sentence_embeddings']
        return result
    except Exception as e:

        return None
    
# 定义文件夹路径
pdfdocx_path = ['D:\\JAVA\\workspace\需求\\产品知识技术问答-需求\\预处理文档\\11月新切分']


# 遍历文件夹中的所有文件
for folder_path in pdfdocx_path:
    print(f"正在导入{folder_path}")
    id = 0
    except_filenames = set()
    with open('D:\JAVA\workspace\AI\ZTE_AI\zte-ibo-acm-productretrieve_0804\exceptmid08151.txt', 'r', encoding='utf-8') as f:
        for line in f:
            except_filenames.add(line.strip())
    for filename in os.listdir(folder_path):
        # 检查文件是否为 txt 文件
        if filename.endswith(".txt"):
            # if str(filename) not in except_filenames:
            #     continue  # 如果在 except.txt 中，跳过该文件
            file_path = os.path.join(folder_path, filename)
            # 打开文件
            content_list = []
            id_list = []
            vec_list = []
            doc_name_list = []
            with open(file_path, "r", encoding='utf-8') as file:
                id +=1
                # 逐行读取文件内容
                doc_name = eval(file.readline())['content']
                file.seek(0)
                for line in file:
                    # 尝试解析行内容为字典
                    try:
                        data = eval(line.strip())  # 使用 eval 函数解析字典字符串
                        # 取出 content 和 id 进行处理
                        content = data.get('content', '')
                        content =truncate_string(content,65535)
                        document_id = data.get('id', '')
                        content_list.append(content)
                        id_list.append(document_id)
                        doc_name_list.append(doc_name)
                        # try:
                        #     vec_1 = embedding_doc(content)
                        #     vec_list.append(vec_1)
                        # except Exception as e:
                        #     with open(except_file_path, "a+") as file1: 
                        #         file1.write(repr(line)+"\n")
                        #     content_list.pop()
                        #     id_list.pop()
                        #     continue
                        # 在这里对 content 和 id 进行处理
                        # print("Content:", content)
                        # print("ID:", document_id)
                    except Exception as e:
                        print("Error processing line:", e)
                try:
                    index = 0  # 初始化索引位置
                    # 每次取五个元素，直到列表被取尽或索引超过列表长度
                    while index < len(content_list):
                        batch = content_list[index:index+1]  # 取五个元素
                        vec_batch = embedding_doc(batch)
                        if not vec_batch:
                            print(f"在存储第{id}个文档时显存溢出")
                            raise Exception("显存溢出")
                        vec_list+=vec_batch
                        index += 1
                except Exception as e:
                    with open(except_file_path, "a+") as file1: 
                        file1.write(str(filename)+"\n")
                    continue
                    
                try:
                    if len(content_list)==len(vec_list) and len(vec_list)==len(id_list) and len(doc_name_list)==len(id_list):
                        milvushelper.insert_doc(collection_name, id_list, vec_list,content_list,doc_name_list)
                except Exception as e:
                    with open(except_file_path, "a+") as file1: 
                        file1.write(str(filename)+"\n")
                    continue
                
                print(f"第{id}个文档存储成功")
print("存储成功")